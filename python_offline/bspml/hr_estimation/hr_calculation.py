"""
Heart Rate Calculation Module

Converts detected PPG peaks into instantaneous heart rate measurements
and provides statistical analysis of heart rate variability.
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from scipy import interpolate, signal
import logging

logger = logging.getLogger(__name__)


def calculate_instantaneous_hr(
    peak_indices: np.ndarray,
    sampling_rate: float,
    interpolation_rate: Optional[float] = None,
    method: str = 'linear'
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate instantaneous heart rate from detected peaks.

    Args:
        peak_indices: Array of peak indices
        sampling_rate: Signal sampling rate in Hz
        interpolation_rate: Target interpolation rate in Hz (optional)
        method: Interpolation method ('linear', 'cubic', 'nearest')

    Returns:
        Tuple of (time_points, hr_values) in seconds and BPM
    """
    if len(peak_indices) < 2:
        raise ValueError("Need at least 2 peaks to calculate heart rate")
    
    # Calculate inter-beat intervals (IBI) in seconds
    ibi_samples = np.diff(peak_indices)
    ibi_seconds = ibi_samples / sampling_rate
    
    # Convert IBI to heart rate in BPM
    hr_bpm = 60.0 / ibi_seconds
    
    # Time points for heart rate values (at peak locations)
    peak_times = peak_indices[1:] / sampling_rate  # Skip first peak
    
    # Filter physiologically plausible heart rates
    valid_mask = (hr_bpm >= 30) & (hr_bpm <= 200)
    valid_times = peak_times[valid_mask]
    valid_hr = hr_bpm[valid_mask]
    
    if len(valid_hr) == 0:
        raise ValueError("No valid heart rate values found")
    
    # Interpolate to regular time grid if requested
    if interpolation_rate is not None:
        time_start = valid_times[0]
        time_end = valid_times[-1]
        
        # Create regular time grid
        regular_times = np.arange(time_start, time_end, 1.0 / interpolation_rate)
        
        if len(regular_times) > 1:
            # Interpolate heart rate values
            if method == 'linear':
                interp_func = interpolate.interp1d(
                    valid_times, valid_hr, kind='linear', 
                    bounds_error=False, fill_value='extrapolate'
                )
            elif method == 'cubic':
                if len(valid_hr) >= 4:  # Need at least 4 points for cubic
                    interp_func = interpolate.interp1d(
                        valid_times, valid_hr, kind='cubic',
                        bounds_error=False, fill_value='extrapolate'
                    )
                else:
                    # Fall back to linear if not enough points
                    interp_func = interpolate.interp1d(
                        valid_times, valid_hr, kind='linear',
                        bounds_error=False, fill_value='extrapolate'
                    )
            elif method == 'nearest':
                interp_func = interpolate.interp1d(
                    valid_times, valid_hr, kind='nearest',
                    bounds_error=False, fill_value='extrapolate'
                )
            else:
                raise ValueError(f"Unknown interpolation method: {method}")
            
            interpolated_hr = interp_func(regular_times)
            
            # Ensure interpolated values are within reasonable range
            interpolated_hr = np.clip(interpolated_hr, 30, 200)
            
            return regular_times, interpolated_hr
    
    return valid_times, valid_hr


def calculate_hr_statistics(
    hr_values: np.ndarray,
    time_points: Optional[np.ndarray] = None
) -> Dict[str, float]:
    """
    Calculate comprehensive heart rate statistics.
    
    Args:
        hr_values: Heart rate values in BPM
        time_points: Time points corresponding to HR values (optional)
        
    Returns:
        Dictionary containing HR statistics
    """
    if len(hr_values) == 0:
        return {}
    
    # Basic statistics
    stats = {
        'mean_hr_bpm': float(np.mean(hr_values)),
        'median_hr_bpm': float(np.median(hr_values)),
        'std_hr_bpm': float(np.std(hr_values)),
        'min_hr_bpm': float(np.min(hr_values)),
        'max_hr_bpm': float(np.max(hr_values)),
        'range_hr_bpm': float(np.ptp(hr_values)),
        'cv_hr': float(np.std(hr_values) / np.mean(hr_values)) if np.mean(hr_values) > 0 else 0
    }
    
    # Percentiles
    percentiles = [5, 25, 75, 95]
    for p in percentiles:
        stats[f'hr_p{p}_bpm'] = float(np.percentile(hr_values, p))
    
    # Heart rate variability metrics (if time points available)
    if time_points is not None and len(time_points) == len(hr_values):
        # Calculate RMSSD (root mean square of successive differences)
        hr_diffs = np.diff(hr_values)
        stats['rmssd_bpm'] = float(np.sqrt(np.mean(hr_diffs**2)))
        
        # Calculate pNN50 (percentage of successive RR intervals that differ by > 50ms)
        # Convert HR differences to RR interval differences (approximate)
        rr_diffs_ms = np.abs(hr_diffs) * 1000 / (hr_values[:-1]**2 / 3600)  # Approximate conversion
        stats['pnn50_percent'] = float(np.sum(rr_diffs_ms > 50) / len(rr_diffs_ms) * 100)
        

    
    return stats






